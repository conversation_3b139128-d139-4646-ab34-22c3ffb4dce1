"use strict";
// Original file: proto/channelz.proto
Object.defineProperty(exports, "__esModule", { value: true });
exports._grpc_channelz_v1_ChannelConnectivityState_State = void 0;
// Original file: proto/channelz.proto
exports._grpc_channelz_v1_ChannelConnectivityState_State = {
    UNKNOWN: 'UNKNOWN',
    IDLE: 'IDLE',
    CONNECTING: 'CONNECTING',
    READY: 'READY',
    TRANSIENT_FAILURE: 'TRANSIENT_FAILURE',
    SHUTDOWN: 'SHUTDOWN',
};
//# sourceMappingURL=ChannelConnectivityState.js.map