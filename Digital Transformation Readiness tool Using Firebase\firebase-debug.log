[debug] [2025-07-09T21:40:15.461Z] ----------------------------------------------------------------------
[debug] [2025-07-09T21:40:15.466Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js emulators:start
[debug] [2025-07-09T21:40:15.467Z] CLI Version:   14.9.0
[debug] [2025-07-09T21:40:15.467Z] Platform:      win32
[debug] [2025-07-09T21:40:15.468Z] Node Version:  v20.17.0
[debug] [2025-07-09T21:40:15.468Z] Time:          Thu Jul 10 2025 03:10:15 GMT+0530 (India Standard Time)
[debug] [2025-07-09T21:40:15.468Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-09T21:40:15.873Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-09T21:40:15.874Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-09T21:40:16.006Z] java version "24.0.1" 2025-04-15

[debug] [2025-07-09T21:40:16.006Z] Java(TM) SE Runtime Environment (build 24.0.1+9-30)
Java HotSpot(TM) 64-Bit Server VM (build 24.0.1+9-30, mixed mode, sharing)

[debug] [2025-07-09T21:40:16.028Z] Parsed Java major version: 24
[info] i  emulators: Starting emulators: auth, functions, firestore, hosting, extensions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: auth, functions, firestore, hosting, extensions"}}
[debug] [2025-07-09T21:40:16.032Z] Checked if tokens are valid: true, expires at: 1752100242583
[debug] [2025-07-09T21:40:16.032Z] Checked if tokens are valid: true, expires at: 1752100242583
[debug] [2025-07-09T21:40:16.034Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/dr-readiness-tool-ons [none]
[debug] [2025-07-09T21:40:17.503Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/dr-readiness-tool-ons 200
[debug] [2025-07-09T21:40:17.503Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/dr-readiness-tool-ons {"projectNumber":"1043119044773","projectId":"dr-readiness-tool-ons","lifecycleState":"ACTIVE","name":"DT Readiness Tool ONS","labels":{"firebase":"enabled","firebase-core":"disabled"},"createTime":"2025-07-08T22:35:39.775953Z","parent":{"type":"organization","id":"516534915552"}}
[debug] [2025-07-09T21:40:17.536Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:40:17.536Z] [auth] Authentication Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:40:17.537Z] [firestore] Firestore Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:40:17.537Z] [firestore.websocket] websocket server for firestore only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:40:17.537Z] [hosting] Hosting Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:40:17.537Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"auth":[{"address":"127.0.0.1","family":"IPv4","port":9099}],"firestore":[{"address":"127.0.0.1","family":"IPv4","port":8081}],"firestore.websocket":[{"address":"127.0.0.1","family":"IPv4","port":9150}],"hosting":[{"address":"127.0.0.1","family":"IPv4","port":5000}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-07-09T21:40:17.545Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-dr-readiness-tool-ons.json
[debug] [2025-07-09T21:40:17.556Z] [Extensions] Started Extensions emulator, this is a noop.
[debug] [2025-07-09T21:40:17.561Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:40:17.561Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:40:17.561Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:40:17.561Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"auth":[{"address":"127.0.0.1","family":"IPv4","port":9099}],"firestore":[{"address":"127.0.0.1","family":"IPv4","port":8081}],"firestore.websocket":[{"address":"127.0.0.1","family":"IPv4","port":9150}],"hosting":[{"address":"127.0.0.1","family":"IPv4","port":5000}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] !  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, database, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, database, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-07-09T21:40:17.596Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\Chaitanya_opennetworksolutions_in_application_default_credentials.json
[debug] [2025-07-09T21:40:17.599Z] Setting GAC to C:\Users\<USER>\AppData\Roaming\firebase\Chaitanya_opennetworksolutions_in_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to C:\\Users\\<USER>\\AppData\\Roaming\\firebase\\Chaitanya_opennetworksolutions_in_application_default_credentials.json"}}
[debug] [2025-07-09T21:40:17.600Z] Checked if tokens are valid: true, expires at: 1752100242583
[debug] [2025-07-09T21:40:17.601Z] Checked if tokens are valid: true, expires at: 1752100242583
[debug] [2025-07-09T21:40:17.601Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/dr-readiness-tool-ons/adminSdkConfig [none]
[debug] [2025-07-09T21:40:18.206Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/dr-readiness-tool-ons/adminSdkConfig 200
[debug] [2025-07-09T21:40:18.206Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/dr-readiness-tool-ons/adminSdkConfig {"projectId":"dr-readiness-tool-ons","storageBucket":"dr-readiness-tool-ons.firebasestorage.app"}
[debug] [2025-07-09T21:40:18.234Z] Ignoring unsupported arg: auto_download {"metadata":{"emulator":{"name":"firestore"},"message":"Ignoring unsupported arg: auto_download"}}
[debug] [2025-07-09T21:40:18.235Z] Ignoring unsupported arg: single_project_mode_error {"metadata":{"emulator":{"name":"firestore"},"message":"Ignoring unsupported arg: single_project_mode_error"}}
[debug] [2025-07-09T21:40:18.235Z] Starting Firestore Emulator with command {"binary":"java","args":["-Dgoogle.cloud_firestore.debug_log_level=FINE","-Duser.language=en","-jar","C:\\Users\\<USER>\\.cache\\firebase\\emulators\\cloud-firestore-emulator-v1.19.8.jar","--host","127.0.0.1","--port",8081,"--websocket_port",9150,"--project_id","dr-readiness-tool-ons","--rules","C:\\Users\\<USER>\\OneDrive\\Desktop\\python codes\\Test codes\\Test 1\\Digital Transformation Readiness tool Using Firebase\\firestore.rules","--single_project_mode",true,"--functions_emulator","127.0.0.1:5001"],"optionalArgs":["port","webchannel_port","host","rules","websocket_port","functions_emulator","seed_from_export","project_id","single_project_mode"],"joinArgs":false,"shell":false,"port":8081} {"metadata":{"emulator":{"name":"firestore"},"message":"Starting Firestore Emulator with command {\"binary\":\"java\",\"args\":[\"-Dgoogle.cloud_firestore.debug_log_level=FINE\",\"-Duser.language=en\",\"-jar\",\"C:\\\\Users\\\\<USER>\\\\.cache\\\\firebase\\\\emulators\\\\cloud-firestore-emulator-v1.19.8.jar\",\"--host\",\"127.0.0.1\",\"--port\",8081,\"--websocket_port\",9150,\"--project_id\",\"dr-readiness-tool-ons\",\"--rules\",\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\python codes\\\\Test codes\\\\Test 1\\\\Digital Transformation Readiness tool Using Firebase\\\\firestore.rules\",\"--single_project_mode\",true,\"--functions_emulator\",\"127.0.0.1:5001\"],\"optionalArgs\":[\"port\",\"webchannel_port\",\"host\",\"rules\",\"websocket_port\",\"functions_emulator\",\"seed_from_export\",\"project_id\",\"single_project_mode\"],\"joinArgs\":false,\"shell\":false,\"port\":8081}"}}
[info] i  firestore: Firestore Emulator logging to firestore-debug.log {"metadata":{"emulator":{"name":"firestore"},"message":"Firestore Emulator logging to \u001b[1mfirestore-debug.log\u001b[22m"}}
[info] +  firestore: Firestore Emulator UI websocket is running on 9150. {"metadata":{"emulator":{"name":"firestore"},"message":"Firestore Emulator UI websocket is running on 9150."}}
[debug] [2025-07-09T21:40:30.159Z] Checked if tokens are valid: true, expires at: 1752100242583
[debug] [2025-07-09T21:40:30.160Z] Checked if tokens are valid: true, expires at: 1752100242583
[debug] [2025-07-09T21:40:30.160Z] >>> [apiv2][query] GET https://firebasehosting.googleapis.com/v1beta1/projects/dr-readiness-tool-ons/sites 
[debug] [2025-07-09T21:40:31.527Z] <<< [apiv2][status] GET https://firebasehosting.googleapis.com/v1beta1/projects/dr-readiness-tool-ons/sites 200
[debug] [2025-07-09T21:40:31.527Z] <<< [apiv2][body] GET https://firebasehosting.googleapis.com/v1beta1/projects/dr-readiness-tool-ons/sites {"sites":[{"name":"projects/dr-readiness-tool-ons/sites/dr-readiness-tool-ons","defaultUrl":"https://dr-readiness-tool-ons.web.app","type":"DEFAULT_SITE"}]}
[debug] [2025-07-09T21:40:31.527Z] Checked if tokens are valid: true, expires at: 1752100242583
[debug] [2025-07-09T21:40:31.527Z] Checked if tokens are valid: true, expires at: 1752100242583
[debug] [2025-07-09T21:40:31.527Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/dr-readiness-tool-ons/webApps/-/config [none]
[debug] [2025-07-09T21:40:32.522Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/dr-readiness-tool-ons/webApps/-/config 200
[debug] [2025-07-09T21:40:32.523Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/dr-readiness-tool-ons/webApps/-/config {"projectId":"dr-readiness-tool-ons","appId":"1:1043119044773:web:fe0209c45a39e01b1eb54c","storageBucket":"dr-readiness-tool-ons.firebasestorage.app","apiKey":"AIzaSyAgWlXWWN1CGgwPCkELaHJz-A2awxZRDzw","authDomain":"dr-readiness-tool-ons.firebaseapp.com","messagingSenderId":"1043119044773"}
[debug] [2025-07-09T21:40:32.534Z] Checked if tokens are valid: true, expires at: 1752100242583
[debug] [2025-07-09T21:40:32.535Z] Checked if tokens are valid: true, expires at: 1752100242583
[debug] [2025-07-09T21:40:32.535Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/dr-readiness-tool-ons [none]
[debug] [2025-07-09T21:40:33.107Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/dr-readiness-tool-ons 200
[debug] [2025-07-09T21:40:33.108Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/dr-readiness-tool-ons {"projectId":"dr-readiness-tool-ons","projectNumber":"1043119044773","displayName":"DT Readiness Tool ONS","name":"projects/dr-readiness-tool-ons","resources":{"hostingSite":"dr-readiness-tool-ons"},"state":"ACTIVE","etag":"1_5d6edad9-79bf-4588-958e-6576ffa9228d"}
[info] i  hosting[dr-readiness-tool-ons]: Serving hosting files from: dt-readiness-tool-firebase/frontend/dist {"metadata":{"emulator":{"name":"hosting"},"message":"Serving hosting files from: \u001b[1mdt-readiness-tool-firebase/frontend/dist\u001b[22m"}}
[info] +  hosting[dr-readiness-tool-ons]: Local server: http://127.0.0.1:5000 {"metadata":{"emulator":{"name":"hosting"},"message":"Local server: \u001b[4m\u001b[1mhttp://127.0.0.1:5000\u001b[22m\u001b[24m"}}
[debug] [2025-07-09T21:40:33.136Z] [Extensions] Connecting Extensions emulator, this is a noop.
[info] i  functions: Watching "C:\Users\<USER>\OneDrive\Desktop\python codes\Test codes\Test 1\Digital Transformation Readiness tool Using Firebase\functions" for Cloud Functions... {"metadata":{"emulator":{"name":"functions"},"message":"Watching \"C:\\Users\\<USER>\\OneDrive\\Desktop\\python codes\\Test codes\\Test 1\\Digital Transformation Readiness tool Using Firebase\\functions\" for Cloud Functions..."}}
[debug] [2025-07-09T21:40:33.144Z] Validating nodejs source
[debug] [2025-07-09T21:40:35.138Z] > [functions] package.json contents: {
  "name": "functions",
  "scripts": {
    "lint": "eslint --ext .js,.ts .",
    "build": "tsc",
    "build:watch": "tsc --watch",
    "serve": "npm run build && firebase emulators:start --only functions",
    "shell": "npm run build && firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "lib/index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1"
  },
  "devDependencies": {
    "@typescript-eslint/eslint-plugin": "^5.12.0",
    "@typescript-eslint/parser": "^5.12.0",
    "eslint": "^8.9.0",
    "eslint-config-google": "^0.14.0",
    "eslint-plugin-import": "^2.25.4",
    "firebase-functions-test": "^3.1.0",
    "typescript": "^5.7.3"
  },
  "private": true
}
[error] !!  functions: Failed to load function definition from source: FirebaseError: There was an error reading functions\package.json:

 functions\lib\index.js does not exist, can't deploy Cloud Functions {"metadata":{"emulator":{"name":"functions"},"message":"Failed to load function definition from source: FirebaseError: There was an error reading functions\\package.json:\n\n functions\\lib\\index.js does not exist, can't deploy Cloud Functions"}}
[debug] [2025-07-09T21:40:35.147Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] 
┌─────────────────────────────────────────────────────────────┐
│ ✔  All emulators ready! It is now safe to connect your app. │
│ i  View Emulator UI at http://127.0.0.1:4000/               │
└─────────────────────────────────────────────────────────────┘

┌────────────────┬────────────────┬──────────────────────────────────┐
│ Emulator       │ Host:Port      │ View in Emulator UI              │
├────────────────┼────────────────┼──────────────────────────────────┤
│ Authentication │ 127.0.0.1:9099 │ http://127.0.0.1:4000/auth       │
├────────────────┼────────────────┼──────────────────────────────────┤
│ Functions      │ 127.0.0.1:5001 │ http://127.0.0.1:4000/functions  │
├────────────────┼────────────────┼──────────────────────────────────┤
│ Firestore      │ 127.0.0.1:8081 │ http://127.0.0.1:4000/firestore  │
├────────────────┼────────────────┼──────────────────────────────────┤
│ Hosting        │ 127.0.0.1:5000 │ n/a                              │
├────────────────┼────────────────┼──────────────────────────────────┤
│ Extensions     │ 127.0.0.1:5001 │ http://127.0.0.1:4000/extensions │
└────────────────┴────────────────┴──────────────────────────────────┘
  Emulator Hub host: 127.0.0.1 port: 4400
  Other reserved ports: 4500, 9150
┌─────────────────────────┬───────────────┬─────────────────────┐
│ Extension Instance Name │ Extension Ref │ View in Emulator UI │
└─────────────────────────┴───────────────┴─────────────────────┘
Issues? Report them at https://github.com/firebase/firebase-tools/issues and attach the *-debug.log files.
 
[info] i  hosting: 127.0.0.1 - - [09/Jul/2025:21:40:54 +0000] "GET / HTTP/1.1" 200 464 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" {"metadata":{"emulator":{"name":"hosting"},"message":"127.0.0.1 - - [09/Jul/2025:21:40:54 +0000] \"GET / HTTP/1.1\" 200 464 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\""}}
[info] i  hosting: 127.0.0.1 - - [09/Jul/2025:21:40:54 +0000] "GET /assets/index-Dtn62Xmo.css HTTP/1.1" 200 911 "http://127.0.0.1:5000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" {"metadata":{"emulator":{"name":"hosting"},"message":"127.0.0.1 - - [09/Jul/2025:21:40:54 +0000] \"GET /assets/index-Dtn62Xmo.css HTTP/1.1\" 200 911 \"http://127.0.0.1:5000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\""}}
[info] i  hosting: 127.0.0.1 - - [09/Jul/2025:21:40:54 +0000] "GET /assets/index-TaBKaYD_.js HTTP/1.1" 200 - "http://127.0.0.1:5000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" {"metadata":{"emulator":{"name":"hosting"},"message":"127.0.0.1 - - [09/Jul/2025:21:40:54 +0000] \"GET /assets/index-TaBKaYD_.js HTTP/1.1\" 200 - \"http://127.0.0.1:5000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\""}}
[info] i  hosting: 127.0.0.1 - - [09/Jul/2025:21:40:54 +0000] "GET /vite.svg HTTP/1.1" 200 - "http://127.0.0.1:5000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" {"metadata":{"emulator":{"name":"hosting"},"message":"127.0.0.1 - - [09/Jul/2025:21:40:54 +0000] \"GET /vite.svg HTTP/1.1\" 200 - \"http://127.0.0.1:5000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\""}}
[info] i  hosting: 127.0.0.1 - - [09/Jul/2025:21:42:02 +0000] "GET / HTTP/1.1" 200 464 "http://127.0.0.1:4000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" {"metadata":{"emulator":{"name":"hosting"},"message":"127.0.0.1 - - [09/Jul/2025:21:42:02 +0000] \"GET / HTTP/1.1\" 200 464 \"http://127.0.0.1:4000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\""}}
[info] i  hosting: 127.0.0.1 - - [09/Jul/2025:21:42:02 +0000] "GET /assets/index-Dtn62Xmo.css HTTP/1.1" 200 911 "http://127.0.0.1:5000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" {"metadata":{"emulator":{"name":"hosting"},"message":"127.0.0.1 - - [09/Jul/2025:21:42:02 +0000] \"GET /assets/index-Dtn62Xmo.css HTTP/1.1\" 200 911 \"http://127.0.0.1:5000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\""}}
[info] i  hosting: 127.0.0.1 - - [09/Jul/2025:21:42:02 +0000] "GET /assets/index-TaBKaYD_.js HTTP/1.1" 200 - "http://127.0.0.1:5000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" {"metadata":{"emulator":{"name":"hosting"},"message":"127.0.0.1 - - [09/Jul/2025:21:42:02 +0000] \"GET /assets/index-TaBKaYD_.js HTTP/1.1\" 200 - \"http://127.0.0.1:5000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\""}}
[debug] [2025-07-09T21:44:07.142Z] >>> [apiv2][query] POST http://127.0.0.1:5001/functions/projects/dr-readiness-tool-ons/trigger_multicast [none]
[debug] [2025-07-09T21:44:07.143Z] >>> [apiv2][body] POST http://127.0.0.1:5001/functions/projects/dr-readiness-tool-ons/trigger_multicast {"eventId":"ead2797c-1bb6-4362-9257-0b6b65618598","eventType":"providers/firebase.auth/eventTypes/user.create","resource":{"name":"projects/dr-readiness-tool-ons","service":"firebaseauth.googleapis.com"},"params":{},"timestamp":"2025-07-09T21:44:07.140Z","data":{"uid":"ftimmxX3XWWOiOh4gNK1v8Ac3flo","email":"<EMAIL>","emailVerified":false,"displayName":"test","photoURL":"","metadata":{"creationTime":"2025-07-09T21:44:07.137Z","lastSignInTime":"2025-07-09T21:44:07.137Z"},"customClaims":{},"providerData":[{"rawId":"<EMAIL>","providerId":"password","displayName":"test","email":"<EMAIL>","federatedId":"<EMAIL>","photoURL":""}],"mfaInfo":[]}}
[debug] [2025-07-09T21:44:07.158Z] <<< [apiv2][status] POST http://127.0.0.1:5001/functions/projects/dr-readiness-tool-ons/trigger_multicast 200
[debug] [2025-07-09T21:44:07.159Z] <<< [apiv2][body] POST http://127.0.0.1:5001/functions/projects/dr-readiness-tool-ons/trigger_multicast {"status":"multicast_acknowledged"}
