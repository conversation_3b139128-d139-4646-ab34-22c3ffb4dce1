[debug] [2025-07-09T15:06:20.800Z] ----------------------------------------------------------------------
[debug] [2025-07-09T15:06:20.802Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js emulators:start
[debug] [2025-07-09T15:06:20.802Z] CLI Version:   14.9.0
[debug] [2025-07-09T15:06:20.802Z] Platform:      win32
[debug] [2025-07-09T15:06:20.802Z] Node Version:  v20.17.0
[debug] [2025-07-09T15:06:20.802Z] Time:          Wed Jul 09 2025 20:36:20 GMT+0530 (India Standard Time)
[debug] [2025-07-09T15:06:20.803Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-09T15:06:21.093Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-09T15:06:21.093Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-09T15:06:21.184Z] java version "24.0.1" 2025-04-15

[debug] [2025-07-09T15:06:21.184Z] Java(TM) SE Runtime Environment (build 24.0.1+9-30)
Java HotSpot(TM) 64-Bit Server VM (build 24.0.1+9-30, mixed mode, sharing)

[debug] [2025-07-09T15:06:21.197Z] Parsed Java major version: 24
[info] i  emulators: Starting emulators: auth, functions, firestore, hosting, extensions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: auth, functions, firestore, hosting, extensions"}}
[debug] [2025-07-09T15:06:21.199Z] Checked if tokens are valid: false, expires at: 1752071557087
[debug] [2025-07-09T15:06:21.199Z] Checked if tokens are valid: false, expires at: 1752071557087
[debug] [2025-07-09T15:06:21.200Z] > refreshing access token with scopes: []
[debug] [2025-07-09T15:06:21.201Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-09T15:06:21.201Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-09T15:06:21.335Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 400
[debug] [2025-07-09T15:06:21.335Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[error] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[error] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[error] !!  extensions: Unable to look up project number for dr-readiness-tool-ons.
 If this is a real project, ensure that you are logged in and have access to it.
 If this is a fake project, please use a project ID starting with 'demo-' to skip production calls.
 Continuing with a fake project number - secrets and other features that require production access may behave unexpectedly. {"metadata":{"emulator":{"name":"extensions"},"message":"Unable to look up project number for dr-readiness-tool-ons.\n If this is a real project, ensure that you are logged in and have access to it.\n If this is a fake project, please use a project ID starting with 'demo-' to skip production calls.\n Continuing with a fake project number - secrets and other features that require production access may behave unexpectedly."}}
[debug] [2025-07-09T15:06:21.349Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T15:06:21.349Z] [auth] Authentication Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T15:06:21.349Z] [firestore] Firestore Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T15:06:21.349Z] [hosting] Hosting Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T15:06:21.350Z] [firestore.websocket] websocket server for firestore only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T15:06:21.350Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"auth":[{"address":"127.0.0.1","family":"IPv4","port":9099}],"firestore":[{"address":"127.0.0.1","family":"IPv4","port":8081}],"hosting":[{"address":"127.0.0.1","family":"IPv4","port":5000}],"firestore.websocket":[{"address":"127.0.0.1","family":"IPv4","port":9151}]},"metadata":{"message":"assigned listening specs for emulators"}}
[warn] !  emulators: It seems that you are running multiple instances of the emulator suite for project dr-readiness-tool-ons. This may result in unexpected behavior. 
[debug] [2025-07-09T15:06:21.355Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-dr-readiness-tool-ons.json
[debug] [2025-07-09T15:06:21.363Z] [Extensions] Started Extensions emulator, this is a noop.
[debug] [2025-07-09T15:06:21.367Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T15:06:21.367Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T15:06:21.367Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T15:06:21.367Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"auth":[{"address":"127.0.0.1","family":"IPv4","port":9099}],"firestore":[{"address":"127.0.0.1","family":"IPv4","port":8081}],"hosting":[{"address":"127.0.0.1","family":"IPv4","port":5000}],"firestore.websocket":[{"address":"127.0.0.1","family":"IPv4","port":9151}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] !  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, database, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, database, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-07-09T15:06:21.399Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\Chaitanya_opennetworksolutions_in_application_default_credentials.json
[debug] [2025-07-09T15:06:21.400Z] Setting GAC to C:\Users\<USER>\AppData\Roaming\firebase\Chaitanya_opennetworksolutions_in_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to C:\\Users\\<USER>\\AppData\\Roaming\\firebase\\Chaitanya_opennetworksolutions_in_application_default_credentials.json"}}
[debug] [2025-07-09T15:06:21.400Z] Checked if tokens are valid: false, expires at: 1752071557087
[debug] [2025-07-09T15:06:21.401Z] Checked if tokens are valid: false, expires at: 1752071557087
[debug] [2025-07-09T15:06:21.401Z] > refreshing access token with scopes: []
[debug] [2025-07-09T15:06:21.401Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-09T15:06:21.401Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-09T15:06:21.508Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 400
[debug] [2025-07-09T15:06:21.508Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[error] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[error] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[debug] [2025-07-09T15:06:21.509Z] Failed to get Admin SDK config for dr-readiness-tool-ons, falling back to cache Failed to get Admin SDK for Firebase project dr-readiness-tool-ons. Please make sure the project exists and your account has permission to access it. {"name":"FirebaseError","children":[],"exit":2,"message":"Failed to get Admin SDK for Firebase project dr-readiness-tool-ons. Please make sure the project exists and your account has permission to access it.","original":{"name":"FirebaseError","children":[],"exit":1,"message":"Authentication Error: Your credentials are no longer valid. Please run \u001b[1mfirebase login --reauth\u001b[22m\n\nFor CI servers and headless environments, generate a new token with \u001b[1mfirebase login:ci\u001b[22m","status":500},"status":500}
[debug] [2025-07-09T15:06:21.525Z] Ignoring unsupported arg: auto_download {"metadata":{"emulator":{"name":"firestore"},"message":"Ignoring unsupported arg: auto_download"}}
[debug] [2025-07-09T15:06:21.525Z] Ignoring unsupported arg: single_project_mode_error {"metadata":{"emulator":{"name":"firestore"},"message":"Ignoring unsupported arg: single_project_mode_error"}}
[debug] [2025-07-09T15:06:21.526Z] Starting Firestore Emulator with command {"binary":"java","args":["-Dgoogle.cloud_firestore.debug_log_level=FINE","-Duser.language=en","-jar","C:\\Users\\<USER>\\.cache\\firebase\\emulators\\cloud-firestore-emulator-v1.19.8.jar","--host","127.0.0.1","--port",8081,"--websocket_port",9151,"--project_id","dr-readiness-tool-ons","--rules","C:\\Users\\<USER>\\OneDrive\\Desktop\\python codes\\Test codes\\Test 1\\Digital Transformation Readiness tool Using Firebase\\firestore.rules","--single_project_mode",true,"--functions_emulator","127.0.0.1:5001"],"optionalArgs":["port","webchannel_port","host","rules","websocket_port","functions_emulator","seed_from_export","project_id","single_project_mode"],"joinArgs":false,"shell":false,"port":8081} {"metadata":{"emulator":{"name":"firestore"},"message":"Starting Firestore Emulator with command {\"binary\":\"java\",\"args\":[\"-Dgoogle.cloud_firestore.debug_log_level=FINE\",\"-Duser.language=en\",\"-jar\",\"C:\\\\Users\\\\<USER>\\\\.cache\\\\firebase\\\\emulators\\\\cloud-firestore-emulator-v1.19.8.jar\",\"--host\",\"127.0.0.1\",\"--port\",8081,\"--websocket_port\",9151,\"--project_id\",\"dr-readiness-tool-ons\",\"--rules\",\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\python codes\\\\Test codes\\\\Test 1\\\\Digital Transformation Readiness tool Using Firebase\\\\firestore.rules\",\"--single_project_mode\",true,\"--functions_emulator\",\"127.0.0.1:5001\"],\"optionalArgs\":[\"port\",\"webchannel_port\",\"host\",\"rules\",\"websocket_port\",\"functions_emulator\",\"seed_from_export\",\"project_id\",\"single_project_mode\"],\"joinArgs\":false,\"shell\":false,\"port\":8081}"}}
[info] i  firestore: Firestore Emulator logging to firestore-debug.log {"metadata":{"emulator":{"name":"firestore"},"message":"Firestore Emulator logging to \u001b[1mfirestore-debug.log\u001b[22m"}}
[info] +  firestore: Firestore Emulator UI websocket is running on 9151. {"metadata":{"emulator":{"name":"firestore"},"message":"Firestore Emulator UI websocket is running on 9151."}}
[debug] [2025-07-09T15:06:29.584Z] Checked if tokens are valid: false, expires at: 1752071557087
[debug] [2025-07-09T15:06:29.585Z] Checked if tokens are valid: false, expires at: 1752071557087
[debug] [2025-07-09T15:06:29.585Z] > refreshing access token with scopes: []
[debug] [2025-07-09T15:06:29.585Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-09T15:06:29.585Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-09T15:06:29.787Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 400
[debug] [2025-07-09T15:06:29.787Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[error] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[error] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[debug] [2025-07-09T15:06:29.788Z] Failed to list hosting sites
[debug] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[debug] [2025-07-09T15:06:29.788Z] Checked if tokens are valid: false, expires at: 1752071557087
[debug] [2025-07-09T15:06:29.788Z] Checked if tokens are valid: false, expires at: 1752071557087
[debug] [2025-07-09T15:06:29.788Z] > refreshing access token with scopes: []
[debug] [2025-07-09T15:06:29.788Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-09T15:06:29.788Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-09T15:06:29.896Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 400
[debug] [2025-07-09T15:06:29.897Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[error] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[error] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[debug] [2025-07-09T15:06:29.897Z] fetchWebSetup error: FirebaseError: Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[warn] !  hosting: Using web app configuration from cache. 
[debug] [2025-07-09T15:06:29.899Z] Checked if tokens are valid: false, expires at: 1752071557087
[debug] [2025-07-09T15:06:29.899Z] Checked if tokens are valid: false, expires at: 1752071557087
[debug] [2025-07-09T15:06:29.899Z] > refreshing access token with scopes: []
[debug] [2025-07-09T15:06:29.899Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-09T15:06:29.900Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-09T15:06:30.012Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 400
[debug] [2025-07-09T15:06:30.012Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[error] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[error] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[debug] [2025-07-09T15:06:30.013Z] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[debug] [2025-07-09T15:06:30.014Z] Specified "public" directory "frontend/build" does not exist; Deploy to Hosting site "dr-readiness-tool-ons" may fail or be empty.
[info] i  hosting[dr-readiness-tool-ons]: Serving hosting files from: frontend/build {"metadata":{"emulator":{"name":"hosting"},"message":"Serving hosting files from: \u001b[1mfrontend/build\u001b[22m"}}
[info] +  hosting[dr-readiness-tool-ons]: Local server: http://127.0.0.1:5000 {"metadata":{"emulator":{"name":"hosting"},"message":"Local server: \u001b[4m\u001b[1mhttp://127.0.0.1:5000\u001b[22m\u001b[24m"}}
[debug] [2025-07-09T15:06:30.035Z] [Extensions] Connecting Extensions emulator, this is a noop.
[info] i  functions: Watching "C:\Users\<USER>\OneDrive\Desktop\python codes\Test codes\Test 1\Digital Transformation Readiness tool Using Firebase\functions" for Cloud Functions... {"metadata":{"emulator":{"name":"functions"},"message":"Watching \"C:\\Users\\<USER>\\OneDrive\\Desktop\\python codes\\Test codes\\Test 1\\Digital Transformation Readiness tool Using Firebase\\functions\" for Cloud Functions..."}}
[debug] [2025-07-09T15:06:30.044Z] Validating nodejs source
[debug] [2025-07-09T15:06:30.984Z] > [functions] package.json contents: {
  "name": "functions",
  "scripts": {
    "lint": "eslint --ext .js,.ts .",
    "build": "tsc",
    "build:watch": "tsc --watch",
    "serve": "npm run build && firebase emulators:start --only functions",
    "shell": "npm run build && firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "lib/index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1"
  },
  "devDependencies": {
    "@typescript-eslint/eslint-plugin": "^5.12.0",
    "@typescript-eslint/parser": "^5.12.0",
    "eslint": "^8.9.0",
    "eslint-config-google": "^0.14.0",
    "eslint-plugin-import": "^2.25.4",
    "firebase-functions-test": "^3.1.0",
    "typescript": "^5.7.3"
  },
  "private": true
}
[error] !!  functions: Failed to load function definition from source: FirebaseError: There was an error reading functions\package.json:

 functions\lib\index.js does not exist, can't deploy Cloud Functions {"metadata":{"emulator":{"name":"functions"},"message":"Failed to load function definition from source: FirebaseError: There was an error reading functions\\package.json:\n\n functions\\lib\\index.js does not exist, can't deploy Cloud Functions"}}
[debug] [2025-07-09T15:06:30.991Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] 
┌─────────────────────────────────────────────────────────────┐
│ ✔  All emulators ready! It is now safe to connect your app. │
│ i  View Emulator UI at http://127.0.0.1:4000/               │
└─────────────────────────────────────────────────────────────┘

┌────────────────┬────────────────┬──────────────────────────────────┐
│ Emulator       │ Host:Port      │ View in Emulator UI              │
├────────────────┼────────────────┼──────────────────────────────────┤
│ Authentication │ 127.0.0.1:9099 │ http://127.0.0.1:4000/auth       │
├────────────────┼────────────────┼──────────────────────────────────┤
│ Functions      │ 127.0.0.1:5001 │ http://127.0.0.1:4000/functions  │
├────────────────┼────────────────┼──────────────────────────────────┤
│ Firestore      │ 127.0.0.1:8081 │ http://127.0.0.1:4000/firestore  │
├────────────────┼────────────────┼──────────────────────────────────┤
│ Hosting        │ 127.0.0.1:5000 │ n/a                              │
├────────────────┼────────────────┼──────────────────────────────────┤
│ Extensions     │ 127.0.0.1:5001 │ http://127.0.0.1:4000/extensions │
└────────────────┴────────────────┴──────────────────────────────────┘
  Emulator Hub host: 127.0.0.1 port: 4400
  Other reserved ports: 4500, 9151
┌─────────────────────────┬───────────────┬─────────────────────┐
│ Extension Instance Name │ Extension Ref │ View in Emulator UI │
└─────────────────────────┴───────────────┴─────────────────────┘
Issues? Report them at https://github.com/firebase/firebase-tools/issues and attach the *-debug.log files.
 
[warn] !  Received a signed JWT. Auth Emulator does not validate JWTs and IS NOT SECURE {"metadata":{"emulator":{"name":"auth"},"message":"Received a signed JWT. Auth Emulator does not validate JWTs and IS NOT SECURE"}}
[debug] [2025-07-09T15:08:23.211Z] >>> [apiv2][query] POST http://127.0.0.1:5001/functions/projects/dr-readiness-tool-ons/trigger_multicast [none]
[debug] [2025-07-09T15:08:23.211Z] >>> [apiv2][body] POST http://127.0.0.1:5001/functions/projects/dr-readiness-tool-ons/trigger_multicast {"eventId":"5b680a33-65d2-44f3-a29a-bd8a82aa09b5","eventType":"providers/firebase.auth/eventTypes/user.create","resource":{"name":"projects/dr-readiness-tool-ons","service":"firebaseauth.googleapis.com"},"params":{},"timestamp":"2025-07-09T15:08:23.210Z","data":{"uid":"Eb25yw2yU1WiiXDL89ZCiSWy52zV","email":"<EMAIL>","emailVerified":false,"metadata":{"creationTime":"2025-07-09T15:08:23.208Z","lastSignInTime":"2025-07-09T15:08:23.208Z"},"customClaims":{},"providerData":[{"rawId":"<EMAIL>","providerId":"password","email":"<EMAIL>","federatedId":"<EMAIL>"}]}}
[debug] [2025-07-09T15:08:23.225Z] <<< [apiv2][status] POST http://127.0.0.1:5001/functions/projects/dr-readiness-tool-ons/trigger_multicast 200
[debug] [2025-07-09T15:08:23.225Z] <<< [apiv2][body] POST http://127.0.0.1:5001/functions/projects/dr-readiness-tool-ons/trigger_multicast {"status":"multicast_acknowledged"}
[debug] [2025-07-09T15:13:10.242Z] Received signal SIGHUP 1
[info]  
[info] i  emulators: Received SIGHUP for the first time. Starting a clean shutdown. 
[info] i  emulators: Please wait for a clean shutdown or send the SIGHUP signal again to stop right now. 
[info] i  emulators: Shutting down emulators. {"metadata":{"emulator":{"name":"hub"},"message":"Shutting down emulators."}}
[info] i  ui: Stopping Emulator UI {"metadata":{"emulator":{"name":"ui"},"message":"Stopping Emulator UI"}}
[info] i  extensions: Stopping Extensions Emulator {"metadata":{"emulator":{"name":"extensions"},"message":"Stopping Extensions Emulator"}}
[debug] [2025-07-09T15:13:10.252Z] [Extensions] Stopping Extensions emulator, this is a noop.
[info] i  functions: Stopping Functions Emulator {"metadata":{"emulator":{"name":"functions"},"message":"Stopping Functions Emulator"}}
[info] i  hosting: Stopping Hosting Emulator {"metadata":{"emulator":{"name":"hosting"},"message":"Stopping Hosting Emulator"}}
[info] i  firestore: Stopping Firestore Emulator {"metadata":{"emulator":{"name":"firestore"},"message":"Stopping Firestore Emulator"}}
[warn] !  Firestore Emulator has exited upon receiving signal: SIGINT 
[info] i  auth: Stopping Authentication Emulator {"metadata":{"emulator":{"name":"auth"},"message":"Stopping Authentication Emulator"}}
[info] i  eventarc: Stopping Eventarc Emulator {"metadata":{"emulator":{"name":"eventarc"},"message":"Stopping Eventarc Emulator"}}
[info] i  tasks: Stopping Cloud Tasks Emulator {"metadata":{"emulator":{"name":"tasks"},"message":"Stopping Cloud Tasks Emulator"}}
[info] i  hub: Stopping emulator hub {"metadata":{"emulator":{"name":"hub"},"message":"Stopping emulator hub"}}
[info] i  logging: Stopping Logging Emulator {"metadata":{"emulator":{"name":"logging"},"message":"Stopping Logging Emulator"}}
[debug] [2025-07-09T15:13:10.272Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[debug] [2025-07-09T21:10:42.513Z] ----------------------------------------------------------------------
[debug] [2025-07-09T21:10:42.518Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js emulators:start
[debug] [2025-07-09T21:10:42.520Z] CLI Version:   14.9.0
[debug] [2025-07-09T21:10:42.520Z] Platform:      win32
[debug] [2025-07-09T21:10:42.520Z] Node Version:  v20.17.0
[debug] [2025-07-09T21:10:42.521Z] Time:          Thu Jul 10 2025 02:40:42 GMT+0530 (India Standard Time)
[debug] [2025-07-09T21:10:42.521Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-09T21:10:42.895Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-09T21:10:42.896Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-09T21:10:43.043Z] java version "24.0.1" 2025-04-15

[debug] [2025-07-09T21:10:43.044Z] Java(TM) SE Runtime Environment (build 24.0.1+9-30)
Java HotSpot(TM) 64-Bit Server VM (build 24.0.1+9-30, mixed mode, sharing)

[debug] [2025-07-09T21:10:43.065Z] Parsed Java major version: 24
[info] i  emulators: Starting emulators: auth, functions, firestore, hosting, extensions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: auth, functions, firestore, hosting, extensions"}}
[debug] [2025-07-09T21:10:43.077Z] Checked if tokens are valid: false, expires at: 1752071557087
[debug] [2025-07-09T21:10:43.078Z] Checked if tokens are valid: false, expires at: 1752071557087
[debug] [2025-07-09T21:10:43.079Z] > refreshing access token with scopes: []
[debug] [2025-07-09T21:10:43.082Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-09T21:10:43.083Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-09T21:10:43.240Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 400
[debug] [2025-07-09T21:10:43.240Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[error] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[error] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[error] !!  extensions: Unable to look up project number for dr-readiness-tool-ons.
 If this is a real project, ensure that you are logged in and have access to it.
 If this is a fake project, please use a project ID starting with 'demo-' to skip production calls.
 Continuing with a fake project number - secrets and other features that require production access may behave unexpectedly. {"metadata":{"emulator":{"name":"extensions"},"message":"Unable to look up project number for dr-readiness-tool-ons.\n If this is a real project, ensure that you are logged in and have access to it.\n If this is a fake project, please use a project ID starting with 'demo-' to skip production calls.\n Continuing with a fake project number - secrets and other features that require production access may behave unexpectedly."}}
[debug] [2025-07-09T21:10:43.271Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:10:43.271Z] [auth] Authentication Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:10:43.272Z] [firestore] Firestore Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:10:43.272Z] [firestore.websocket] websocket server for firestore only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:10:43.272Z] [hosting] Hosting Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:10:43.272Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"auth":[{"address":"127.0.0.1","family":"IPv4","port":9099}],"firestore":[{"address":"127.0.0.1","family":"IPv4","port":8081}],"firestore.websocket":[{"address":"127.0.0.1","family":"IPv4","port":9150}],"hosting":[{"address":"127.0.0.1","family":"IPv4","port":5000}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-07-09T21:10:43.279Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-dr-readiness-tool-ons.json
[debug] [2025-07-09T21:10:43.293Z] [Extensions] Started Extensions emulator, this is a noop.
[debug] [2025-07-09T21:10:43.299Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:10:43.300Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:10:43.300Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:10:43.300Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"auth":[{"address":"127.0.0.1","family":"IPv4","port":9099}],"firestore":[{"address":"127.0.0.1","family":"IPv4","port":8081}],"firestore.websocket":[{"address":"127.0.0.1","family":"IPv4","port":9150}],"hosting":[{"address":"127.0.0.1","family":"IPv4","port":5000}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] !  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, database, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, database, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-07-09T21:10:43.347Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\Chaitanya_opennetworksolutions_in_application_default_credentials.json
[debug] [2025-07-09T21:10:43.349Z] Setting GAC to C:\Users\<USER>\AppData\Roaming\firebase\Chaitanya_opennetworksolutions_in_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to C:\\Users\\<USER>\\AppData\\Roaming\\firebase\\Chaitanya_opennetworksolutions_in_application_default_credentials.json"}}
[debug] [2025-07-09T21:10:43.349Z] Checked if tokens are valid: false, expires at: 1752071557087
[debug] [2025-07-09T21:10:43.349Z] Checked if tokens are valid: false, expires at: 1752071557087
[debug] [2025-07-09T21:10:43.349Z] > refreshing access token with scopes: []
[debug] [2025-07-09T21:10:43.350Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-09T21:10:43.350Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-09T21:10:43.548Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 400
[debug] [2025-07-09T21:10:43.548Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[error] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[error] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[debug] [2025-07-09T21:10:43.549Z] Failed to get Admin SDK config for dr-readiness-tool-ons, falling back to cache Failed to get Admin SDK for Firebase project dr-readiness-tool-ons. Please make sure the project exists and your account has permission to access it. {"name":"FirebaseError","children":[],"exit":2,"message":"Failed to get Admin SDK for Firebase project dr-readiness-tool-ons. Please make sure the project exists and your account has permission to access it.","original":{"name":"FirebaseError","children":[],"exit":1,"message":"Authentication Error: Your credentials are no longer valid. Please run \u001b[1mfirebase login --reauth\u001b[22m\n\nFor CI servers and headless environments, generate a new token with \u001b[1mfirebase login:ci\u001b[22m","status":500},"status":500}
[debug] [2025-07-09T21:10:43.568Z] Ignoring unsupported arg: auto_download {"metadata":{"emulator":{"name":"firestore"},"message":"Ignoring unsupported arg: auto_download"}}
[debug] [2025-07-09T21:10:43.568Z] Ignoring unsupported arg: single_project_mode_error {"metadata":{"emulator":{"name":"firestore"},"message":"Ignoring unsupported arg: single_project_mode_error"}}
[debug] [2025-07-09T21:10:43.568Z] Starting Firestore Emulator with command {"binary":"java","args":["-Dgoogle.cloud_firestore.debug_log_level=FINE","-Duser.language=en","-jar","C:\\Users\\<USER>\\.cache\\firebase\\emulators\\cloud-firestore-emulator-v1.19.8.jar","--host","127.0.0.1","--port",8081,"--websocket_port",9150,"--project_id","dr-readiness-tool-ons","--rules","C:\\Users\\<USER>\\OneDrive\\Desktop\\python codes\\Test codes\\Test 1\\Digital Transformation Readiness tool Using Firebase\\firestore.rules","--single_project_mode",true,"--functions_emulator","127.0.0.1:5001"],"optionalArgs":["port","webchannel_port","host","rules","websocket_port","functions_emulator","seed_from_export","project_id","single_project_mode"],"joinArgs":false,"shell":false,"port":8081} {"metadata":{"emulator":{"name":"firestore"},"message":"Starting Firestore Emulator with command {\"binary\":\"java\",\"args\":[\"-Dgoogle.cloud_firestore.debug_log_level=FINE\",\"-Duser.language=en\",\"-jar\",\"C:\\\\Users\\\\<USER>\\\\.cache\\\\firebase\\\\emulators\\\\cloud-firestore-emulator-v1.19.8.jar\",\"--host\",\"127.0.0.1\",\"--port\",8081,\"--websocket_port\",9150,\"--project_id\",\"dr-readiness-tool-ons\",\"--rules\",\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\python codes\\\\Test codes\\\\Test 1\\\\Digital Transformation Readiness tool Using Firebase\\\\firestore.rules\",\"--single_project_mode\",true,\"--functions_emulator\",\"127.0.0.1:5001\"],\"optionalArgs\":[\"port\",\"webchannel_port\",\"host\",\"rules\",\"websocket_port\",\"functions_emulator\",\"seed_from_export\",\"project_id\",\"single_project_mode\"],\"joinArgs\":false,\"shell\":false,\"port\":8081}"}}
[info] i  firestore: Firestore Emulator logging to firestore-debug.log {"metadata":{"emulator":{"name":"firestore"},"message":"Firestore Emulator logging to \u001b[1mfirestore-debug.log\u001b[22m"}}
[info] +  firestore: Firestore Emulator UI websocket is running on 9150. {"metadata":{"emulator":{"name":"firestore"},"message":"Firestore Emulator UI websocket is running on 9150."}}
[debug] [2025-07-09T21:10:56.039Z] Checked if tokens are valid: false, expires at: 1752071557087
[debug] [2025-07-09T21:10:56.040Z] Checked if tokens are valid: false, expires at: 1752071557087
[debug] [2025-07-09T21:10:56.040Z] > refreshing access token with scopes: []
[debug] [2025-07-09T21:10:56.040Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-09T21:10:56.041Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-09T21:10:56.237Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 400
[debug] [2025-07-09T21:10:56.238Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[error] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[error] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[debug] [2025-07-09T21:10:56.239Z] Failed to list hosting sites
[debug] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[debug] [2025-07-09T21:10:56.239Z] Checked if tokens are valid: false, expires at: 1752071557087
[debug] [2025-07-09T21:10:56.239Z] Checked if tokens are valid: false, expires at: 1752071557087
[debug] [2025-07-09T21:10:56.240Z] > refreshing access token with scopes: []
[debug] [2025-07-09T21:10:56.240Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-09T21:10:56.240Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-09T21:10:56.651Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 400
[debug] [2025-07-09T21:10:56.651Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[error] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[error] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[debug] [2025-07-09T21:10:56.653Z] fetchWebSetup error: FirebaseError: Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[warn] !  hosting: Using web app configuration from cache. 
[debug] [2025-07-09T21:10:56.656Z] Checked if tokens are valid: false, expires at: 1752071557087
[debug] [2025-07-09T21:10:56.656Z] Checked if tokens are valid: false, expires at: 1752071557087
[debug] [2025-07-09T21:10:56.656Z] > refreshing access token with scopes: []
[debug] [2025-07-09T21:10:56.657Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-09T21:10:56.657Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-09T21:10:56.764Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 400
[debug] [2025-07-09T21:10:56.765Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[error] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[error] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[debug] [2025-07-09T21:10:56.766Z] Authentication Error: Your credentials are no longer valid. Please run firebase login --reauth

For CI servers and headless environments, generate a new token with firebase login:ci
[debug] [2025-07-09T21:10:56.768Z] Specified "public" directory "frontend/build" does not exist; Deploy to Hosting site "dr-readiness-tool-ons" may fail or be empty.
[info] i  hosting[dr-readiness-tool-ons]: Serving hosting files from: frontend/build {"metadata":{"emulator":{"name":"hosting"},"message":"Serving hosting files from: \u001b[1mfrontend/build\u001b[22m"}}
[info] +  hosting[dr-readiness-tool-ons]: Local server: http://127.0.0.1:5000 {"metadata":{"emulator":{"name":"hosting"},"message":"Local server: \u001b[4m\u001b[1mhttp://127.0.0.1:5000\u001b[22m\u001b[24m"}}
[debug] [2025-07-09T21:10:56.793Z] [Extensions] Connecting Extensions emulator, this is a noop.
[info] i  functions: Watching "C:\Users\<USER>\OneDrive\Desktop\python codes\Test codes\Test 1\Digital Transformation Readiness tool Using Firebase\functions" for Cloud Functions... {"metadata":{"emulator":{"name":"functions"},"message":"Watching \"C:\\Users\\<USER>\\OneDrive\\Desktop\\python codes\\Test codes\\Test 1\\Digital Transformation Readiness tool Using Firebase\\functions\" for Cloud Functions..."}}
[debug] [2025-07-09T21:10:56.802Z] Validating nodejs source
[debug] [2025-07-09T21:10:57.863Z] > [functions] package.json contents: {
  "name": "functions",
  "scripts": {
    "lint": "eslint --ext .js,.ts .",
    "build": "tsc",
    "build:watch": "tsc --watch",
    "serve": "npm run build && firebase emulators:start --only functions",
    "shell": "npm run build && firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "lib/index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1"
  },
  "devDependencies": {
    "@typescript-eslint/eslint-plugin": "^5.12.0",
    "@typescript-eslint/parser": "^5.12.0",
    "eslint": "^8.9.0",
    "eslint-config-google": "^0.14.0",
    "eslint-plugin-import": "^2.25.4",
    "firebase-functions-test": "^3.1.0",
    "typescript": "^5.7.3"
  },
  "private": true
}
[error] !!  functions: Failed to load function definition from source: FirebaseError: There was an error reading functions\package.json:

 functions\lib\index.js does not exist, can't deploy Cloud Functions {"metadata":{"emulator":{"name":"functions"},"message":"Failed to load function definition from source: FirebaseError: There was an error reading functions\\package.json:\n\n functions\\lib\\index.js does not exist, can't deploy Cloud Functions"}}
[debug] [2025-07-09T21:10:57.871Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] 
┌─────────────────────────────────────────────────────────────┐
│ ✔  All emulators ready! It is now safe to connect your app. │
│ i  View Emulator UI at http://127.0.0.1:4000/               │
└─────────────────────────────────────────────────────────────┘

┌────────────────┬────────────────┬──────────────────────────────────┐
│ Emulator       │ Host:Port      │ View in Emulator UI              │
├────────────────┼────────────────┼──────────────────────────────────┤
│ Authentication │ 127.0.0.1:9099 │ http://127.0.0.1:4000/auth       │
├────────────────┼────────────────┼──────────────────────────────────┤
│ Functions      │ 127.0.0.1:5001 │ http://127.0.0.1:4000/functions  │
├────────────────┼────────────────┼──────────────────────────────────┤
│ Firestore      │ 127.0.0.1:8081 │ http://127.0.0.1:4000/firestore  │
├────────────────┼────────────────┼──────────────────────────────────┤
│ Hosting        │ 127.0.0.1:5000 │ n/a                              │
├────────────────┼────────────────┼──────────────────────────────────┤
│ Extensions     │ 127.0.0.1:5001 │ http://127.0.0.1:4000/extensions │
└────────────────┴────────────────┴──────────────────────────────────┘
  Emulator Hub host: 127.0.0.1 port: 4400
  Other reserved ports: 4500, 9150
┌─────────────────────────┬───────────────┬─────────────────────┐
│ Extension Instance Name │ Extension Ref │ View in Emulator UI │
└─────────────────────────┴───────────────┴─────────────────────┘
Issues? Report them at https://github.com/firebase/firebase-tools/issues and attach the *-debug.log files.
 
