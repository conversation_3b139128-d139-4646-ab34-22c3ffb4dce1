[debug] [2025-07-09T21:34:16.298Z] ----------------------------------------------------------------------
[debug] [2025-07-09T21:34:16.304Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js emulators:start
[debug] [2025-07-09T21:34:16.304Z] CLI Version:   14.9.0
[debug] [2025-07-09T21:34:16.305Z] Platform:      win32
[debug] [2025-07-09T21:34:16.305Z] Node Version:  v20.17.0
[debug] [2025-07-09T21:34:16.305Z] Time:          Thu Jul 10 2025 03:04:16 GMT+0530 (India Standard Time)
[debug] [2025-07-09T21:34:16.305Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-09T21:34:16.678Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-09T21:34:16.678Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-09T21:34:16.817Z] java version "24.0.1" 2025-04-15

[debug] [2025-07-09T21:34:16.819Z] Java(TM) SE Runtime Environment (build 24.0.1+9-30)
Java HotSpot(TM) 64-Bit Server VM (build 24.0.1+9-30, mixed mode, sharing)

[debug] [2025-07-09T21:34:16.837Z] Parsed Java major version: 24
[info] i  emulators: Starting emulators: auth, functions, firestore, hosting, extensions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: auth, functions, firestore, hosting, extensions"}}
[debug] [2025-07-09T21:34:16.841Z] Checked if tokens are valid: true, expires at: 1752100242583
[debug] [2025-07-09T21:34:16.841Z] Checked if tokens are valid: true, expires at: 1752100242583
[debug] [2025-07-09T21:34:16.843Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/dr-readiness-tool-ons [none]
[debug] [2025-07-09T21:34:18.325Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/dr-readiness-tool-ons 200
[debug] [2025-07-09T21:34:18.326Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/dr-readiness-tool-ons {"projectNumber":"1043119044773","projectId":"dr-readiness-tool-ons","lifecycleState":"ACTIVE","name":"DT Readiness Tool ONS","labels":{"firebase":"enabled","firebase-core":"disabled"},"createTime":"2025-07-08T22:35:39.775953Z","parent":{"type":"organization","id":"516534915552"}}
[debug] [2025-07-09T21:34:18.349Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:34:18.349Z] [auth] Authentication Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:34:18.349Z] [firestore] Firestore Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:34:18.349Z] [firestore.websocket] websocket server for firestore only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:34:18.349Z] [hosting] Hosting Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:34:18.349Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"auth":[{"address":"127.0.0.1","family":"IPv4","port":9099}],"firestore":[{"address":"127.0.0.1","family":"IPv4","port":8081}],"firestore.websocket":[{"address":"127.0.0.1","family":"IPv4","port":9150}],"hosting":[{"address":"127.0.0.1","family":"IPv4","port":5000}]},"metadata":{"message":"assigned listening specs for emulators"}}
[warn] !  emulators: It seems that you are running multiple instances of the emulator suite for project dr-readiness-tool-ons. This may result in unexpected behavior. 
[debug] [2025-07-09T21:34:18.363Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-dr-readiness-tool-ons.json
[debug] [2025-07-09T21:34:18.374Z] [Extensions] Started Extensions emulator, this is a noop.
[debug] [2025-07-09T21:34:18.380Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:34:18.380Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:34:18.380Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:34:18.380Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"auth":[{"address":"127.0.0.1","family":"IPv4","port":9099}],"firestore":[{"address":"127.0.0.1","family":"IPv4","port":8081}],"firestore.websocket":[{"address":"127.0.0.1","family":"IPv4","port":9150}],"hosting":[{"address":"127.0.0.1","family":"IPv4","port":5000}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] !  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, database, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, database, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-07-09T21:34:18.409Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\Chaitanya_opennetworksolutions_in_application_default_credentials.json
[debug] [2025-07-09T21:34:18.412Z] Setting GAC to C:\Users\<USER>\AppData\Roaming\firebase\Chaitanya_opennetworksolutions_in_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to C:\\Users\\<USER>\\AppData\\Roaming\\firebase\\Chaitanya_opennetworksolutions_in_application_default_credentials.json"}}
[debug] [2025-07-09T21:34:18.413Z] Checked if tokens are valid: true, expires at: 1752100242583
[debug] [2025-07-09T21:34:18.413Z] Checked if tokens are valid: true, expires at: 1752100242583
[debug] [2025-07-09T21:34:18.413Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/dr-readiness-tool-ons/adminSdkConfig [none]
[debug] [2025-07-09T21:34:19.328Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/dr-readiness-tool-ons/adminSdkConfig 200
[debug] [2025-07-09T21:34:19.328Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/dr-readiness-tool-ons/adminSdkConfig {"projectId":"dr-readiness-tool-ons","storageBucket":"dr-readiness-tool-ons.firebasestorage.app"}
[debug] [2025-07-09T21:34:19.358Z] Ignoring unsupported arg: auto_download {"metadata":{"emulator":{"name":"firestore"},"message":"Ignoring unsupported arg: auto_download"}}
[debug] [2025-07-09T21:34:19.358Z] Ignoring unsupported arg: single_project_mode_error {"metadata":{"emulator":{"name":"firestore"},"message":"Ignoring unsupported arg: single_project_mode_error"}}
[debug] [2025-07-09T21:34:19.358Z] Starting Firestore Emulator with command {"binary":"java","args":["-Dgoogle.cloud_firestore.debug_log_level=FINE","-Duser.language=en","-jar","C:\\Users\\<USER>\\.cache\\firebase\\emulators\\cloud-firestore-emulator-v1.19.8.jar","--host","127.0.0.1","--port",8081,"--websocket_port",9150,"--project_id","dr-readiness-tool-ons","--rules","C:\\Users\\<USER>\\OneDrive\\Desktop\\python codes\\Test codes\\Test 1\\Digital Transformation Readiness tool Using Firebase\\firestore.rules","--single_project_mode",true,"--functions_emulator","127.0.0.1:5001"],"optionalArgs":["port","webchannel_port","host","rules","websocket_port","functions_emulator","seed_from_export","project_id","single_project_mode"],"joinArgs":false,"shell":false,"port":8081} {"metadata":{"emulator":{"name":"firestore"},"message":"Starting Firestore Emulator with command {\"binary\":\"java\",\"args\":[\"-Dgoogle.cloud_firestore.debug_log_level=FINE\",\"-Duser.language=en\",\"-jar\",\"C:\\\\Users\\\\<USER>\\\\.cache\\\\firebase\\\\emulators\\\\cloud-firestore-emulator-v1.19.8.jar\",\"--host\",\"127.0.0.1\",\"--port\",8081,\"--websocket_port\",9150,\"--project_id\",\"dr-readiness-tool-ons\",\"--rules\",\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\python codes\\\\Test codes\\\\Test 1\\\\Digital Transformation Readiness tool Using Firebase\\\\firestore.rules\",\"--single_project_mode\",true,\"--functions_emulator\",\"127.0.0.1:5001\"],\"optionalArgs\":[\"port\",\"webchannel_port\",\"host\",\"rules\",\"websocket_port\",\"functions_emulator\",\"seed_from_export\",\"project_id\",\"single_project_mode\"],\"joinArgs\":false,\"shell\":false,\"port\":8081}"}}
[info] i  firestore: Firestore Emulator logging to firestore-debug.log {"metadata":{"emulator":{"name":"firestore"},"message":"Firestore Emulator logging to \u001b[1mfirestore-debug.log\u001b[22m"}}
