[debug] [2025-07-09T21:40:15.461Z] ----------------------------------------------------------------------
[debug] [2025-07-09T21:40:15.466Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js emulators:start
[debug] [2025-07-09T21:40:15.467Z] CLI Version:   14.9.0
[debug] [2025-07-09T21:40:15.467Z] Platform:      win32
[debug] [2025-07-09T21:40:15.468Z] Node Version:  v20.17.0
[debug] [2025-07-09T21:40:15.468Z] Time:          Thu Jul 10 2025 03:10:15 GMT+0530 (India Standard Time)
[debug] [2025-07-09T21:40:15.468Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-09T21:40:15.873Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-09T21:40:15.874Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-09T21:40:16.006Z] java version "24.0.1" 2025-04-15

[debug] [2025-07-09T21:40:16.006Z] Java(TM) SE Runtime Environment (build 24.0.1+9-30)
Java HotSpot(TM) 64-Bit Server VM (build 24.0.1+9-30, mixed mode, sharing)

[debug] [2025-07-09T21:40:16.028Z] Parsed Java major version: 24
[info] i  emulators: Starting emulators: auth, functions, firestore, hosting, extensions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: auth, functions, firestore, hosting, extensions"}}
[debug] [2025-07-09T21:40:16.032Z] Checked if tokens are valid: true, expires at: 1752100242583
[debug] [2025-07-09T21:40:16.032Z] Checked if tokens are valid: true, expires at: 1752100242583
[debug] [2025-07-09T21:40:16.034Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/dr-readiness-tool-ons [none]
[debug] [2025-07-09T21:40:17.503Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/dr-readiness-tool-ons 200
[debug] [2025-07-09T21:40:17.503Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/dr-readiness-tool-ons {"projectNumber":"1043119044773","projectId":"dr-readiness-tool-ons","lifecycleState":"ACTIVE","name":"DT Readiness Tool ONS","labels":{"firebase":"enabled","firebase-core":"disabled"},"createTime":"2025-07-08T22:35:39.775953Z","parent":{"type":"organization","id":"516534915552"}}
[debug] [2025-07-09T21:40:17.536Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:40:17.536Z] [auth] Authentication Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:40:17.537Z] [firestore] Firestore Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:40:17.537Z] [firestore.websocket] websocket server for firestore only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:40:17.537Z] [hosting] Hosting Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:40:17.537Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"auth":[{"address":"127.0.0.1","family":"IPv4","port":9099}],"firestore":[{"address":"127.0.0.1","family":"IPv4","port":8081}],"firestore.websocket":[{"address":"127.0.0.1","family":"IPv4","port":9150}],"hosting":[{"address":"127.0.0.1","family":"IPv4","port":5000}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-07-09T21:40:17.545Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-dr-readiness-tool-ons.json
[debug] [2025-07-09T21:40:17.556Z] [Extensions] Started Extensions emulator, this is a noop.
[debug] [2025-07-09T21:40:17.561Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:40:17.561Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:40:17.561Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-09T21:40:17.561Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"auth":[{"address":"127.0.0.1","family":"IPv4","port":9099}],"firestore":[{"address":"127.0.0.1","family":"IPv4","port":8081}],"firestore.websocket":[{"address":"127.0.0.1","family":"IPv4","port":9150}],"hosting":[{"address":"127.0.0.1","family":"IPv4","port":5000}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] !  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, database, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, database, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-07-09T21:40:17.596Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\Chaitanya_opennetworksolutions_in_application_default_credentials.json
[debug] [2025-07-09T21:40:17.599Z] Setting GAC to C:\Users\<USER>\AppData\Roaming\firebase\Chaitanya_opennetworksolutions_in_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to C:\\Users\\<USER>\\AppData\\Roaming\\firebase\\Chaitanya_opennetworksolutions_in_application_default_credentials.json"}}
[debug] [2025-07-09T21:40:17.600Z] Checked if tokens are valid: true, expires at: 1752100242583
[debug] [2025-07-09T21:40:17.601Z] Checked if tokens are valid: true, expires at: 1752100242583
[debug] [2025-07-09T21:40:17.601Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/dr-readiness-tool-ons/adminSdkConfig [none]
