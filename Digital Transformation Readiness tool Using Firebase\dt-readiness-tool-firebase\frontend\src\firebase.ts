import { initializeApp } from "firebase/app";
import { getAuth, connectAuthEmulator } from "firebase/auth";
import { getFirestore, connectFirestoreEmulator } from "firebase/firestore";
import { firebaseConfig } from "./firebaseConfig";

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
const auth = getAuth(app);
const db = getFirestore(app);

// Connect to emulators in development or when running locally
const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
if (import.meta.env.DEV || isLocalhost) {
  try {
    // Connect to Auth emulator
    connectAuthEmulator(auth, "http://127.0.0.1:9099", {
      disableWarnings: true
    });

    // Connect to Firestore emulator
    connectFirestoreEmulator(db, "127.0.0.1", 8081);

    console.log("Connected to Firebase emulators");
  } catch (error) {
    console.log("Emulators already connected or error:", error);
  }
}

export { auth, db };