import React from 'react';
import {
  Box,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Slider,
  Typography,
  TextField
} from '@mui/material';
import { AssessmentQuestion } from '../utils/seedData';

interface QuestionProps {
  question: AssessmentQuestion;
  currentAnswer?: any;
  onAnswerChange: (questionId: string, answer: any) => void;
}

const Question: React.FC<QuestionProps> = ({ question, currentAnswer, onAnswerChange }) => {
  const handleAnswerChange = (value: any) => {
    onAnswerChange(question.id, value);
  };

  const renderQuestionInput = () => {
    switch (question.questionType) {
      case 'multiple_choice':
        return (
          <FormControl component="fieldset" fullWidth>
            <RadioGroup
              value={currentAnswer || ''}
              onChange={(e) => handleAnswerChange(e.target.value)}
            >
              {question.options?.map((option, index) => {
                const optionValue = typeof option === 'string' ? option : option.value;
                const optionLabel = typeof option === 'string' ? option : option.label;
                
                return (
                  <FormControlLabel
                    key={index}
                    value={optionValue}
                    control={<Radio />}
                    label={optionLabel}
                  />
                );
              })}
            </RadioGroup>
          </FormControl>
        );

      case 'likert_scale':
        const likertOptions = question.options as { value: string; label: string }[];
        const marks = likertOptions?.map((option, index) => ({
          value: parseInt(option.value),
          label: option.label
        })) || [];

        return (
          <Box sx={{ px: 2 }}>
            <Slider
              value={currentAnswer ? parseInt(currentAnswer) : 1}
              onChange={(_, value) => handleAnswerChange(value.toString())}
              step={1}
              marks={marks}
              min={1}
              max={likertOptions?.length || 5}
              valueLabelDisplay="auto"
              valueLabelFormat={(value) => {
                const option = likertOptions?.find(opt => parseInt(opt.value) === value);
                return option ? option.label : value.toString();
              }}
            />
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
              {likertOptions?.map((option, index) => (
                <Typography key={index} variant="caption" color="text.secondary">
                  {option.label}
                </Typography>
              ))}
            </Box>
          </Box>
        );

      case 'yes_no':
        return (
          <FormControl component="fieldset" fullWidth>
            <RadioGroup
              value={currentAnswer || ''}
              onChange={(e) => handleAnswerChange(e.target.value)}
              row
            >
              <FormControlLabel
                value="Yes"
                control={<Radio />}
                label="Yes"
              />
              <FormControlLabel
                value="No"
                control={<Radio />}
                label="No"
              />
            </RadioGroup>
          </FormControl>
        );

      case 'text':
        return (
          <TextField
            fullWidth
            multiline
            rows={3}
            value={currentAnswer || ''}
            onChange={(e) => handleAnswerChange(e.target.value)}
            placeholder="Enter your response..."
            variant="outlined"
          />
        );

      default:
        return (
          <Typography color="error">
            Unsupported question type: {question.questionType}
          </Typography>
        );
    }
  };

  return (
    <Box sx={{ mb: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
        <FormLabel component="legend" sx={{ flexGrow: 1, fontSize: '1.1rem', fontWeight: 500 }}>
          {question.questionText}
          {question.required && (
            <Typography component="span" color="error" sx={{ ml: 0.5 }}>
              *
            </Typography>
          )}
        </FormLabel>
      </Box>

      {question.tooltipText && (
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2, fontStyle: 'italic' }}>
          💡 {question.tooltipText}
        </Typography>
      )}

      {renderQuestionInput()}

      {question.required && !currentAnswer && (
        <Typography variant="caption" color="error" sx={{ mt: 1, display: 'block' }}>
          This question is required
        </Typography>
      )}
    </Box>
  );
};

export default Question;
