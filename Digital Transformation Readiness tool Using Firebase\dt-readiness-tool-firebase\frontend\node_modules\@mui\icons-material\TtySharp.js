"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = exports.default = (0, _createSvgIcon.default)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M20 15v6c-3.28 0-6.35-.89-9-2.43A18 18 0 0 1 4.43 12C2.89 9.35 2 6.28 2 3h6l1 5-2.9 2.9c1.43 2.5 3.5 4.57 6 6L15 14zm-6-9h2V4h-2zm-1 3h2V7h-2zm-2-3h2V4h-2zm7 1h-2v2h2zm1-3h-2v2h2zm2 3h-2v2h2zm1-3h-2v2h2zm-8 8h2v-2h-2zm-3 0h2v-2h-2zm8-2h-2v2h2zm3 0h-2v2h2z"
}), 'TtySharp');